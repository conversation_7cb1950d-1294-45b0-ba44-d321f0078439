﻿بالتأكيد، سأقوم بشرح مفصل للمعلومات الواردة في المصادر التي قدمتها حول هندسة البرمجيات.
تغطي المصادر عدة جوانب رئيسية في هندسة البرمجيات، بدءاً من أهمية البرمجيات وطبيعتها، مروراً بظروف نشأة الهندسة كنظام، وصولاً إلى نماذج تطوير البرمجيات وإدارتها وتحدياتها والقضايا الأخلاقية المرتبطة بها.
1. أهمية البرمجيات (Importance of software) تلعب البرمجيات دوراً أساسياً ومحورياً في حياتنا اليومية. فهي موجودة في مجالات مختلفة مثل المجالات الاقتصادية، الطبية، العسكرية وغيرها. أصبحت البنية التحتية للدول ومرافقها العامة تعتمد على الأنظمة الحاسوبية. المصانع وعمليات التوزيع أصبحت مؤتمتة بشكل شبه كامل أو كامل. الأنظمة التجارية والمالية أصبحت تعتمد بشكل كبير على البرمجيات. كل هذه العوامل أدت إلى أن تصبح عملية إنتاج وصيانة البرمجيات عنصراً جوهرياً في سير الاقتصاد الوطني والعالمي ككل.
2. طبيعة البرمجيات (Nature of software products) تتميز البرمجيات بكونها منتجات مجردة وغير محسوسة. على عكس المنتجات المادية التي تخضع لقيود مادية وقوانين فيزيائية ثابتة ودقيقة، فإن البرمجيات لا تخضع لهذه القيود. ميزة أخرى للبرمجيات هي أنها لا تبلى مع الاستخدام. هذه الطبيعة للبرمجيات أدت إلى زيادة إمكانيات المنتج البرمجي، مما يجعله يستخدم في جميع المجالات ويُطلب لحل مشاكل ليس له دخل فيها. كما أدت إلى ظهور منتجات برمجية معقدة جداً ويصعب فهمها.
3. تاريخ وظروف نشأة هندسة البرمجيات (History and circumstances of the emergence of software engineering) مصطلح "هندسة البرمجيات" استُخدم لأول مرة في مؤتمر عُقد عام 1968 من قبل اللجنة العلمية لحلف الناتو لتداول الأزمة المعروفة بـ "أزمة البرمجيات". الظروف التي صاحبت نشأة هندسة البرمجيات تمثلت في:
* التطور الهائل في صناعة الحواسيب باستخدام الدوائر المتكاملة مع هبوط في أسعار العتاد وارتفاع كبير في أسعار البرمجيات.
* جودة المنتجات البرمجية كانت منخفضة، بأداء ضعيف وغير قابلة للصيانة والتطوير.
* تجاوز المواعيد الزمنية المتفق عليها لتسليم المشاريع البرمجية وعدم كفاية الميزانية المتفق عليها لإنجاز المشاريع.
4. البرمجية والبرنامج (Software and program)
* البرنامج (Program): هو مجموعة من الأكواد البرمجية المكتوبة بلغة معينة لتنفيذ أمر محدد، وهي بحاجة إلى بيئة لتنفيذه.
* البرمجية (Software): هي مجموعة من البرامج بالإضافة إلى ملفات التوثيق الناتجة من مراحل بناء النظام البرمجي (مثل ملفات توثيق المتطلبات، هيكلية النظام البرمجي، ملفات المستخدم). كما تشمل ملفات التكوين (Setup) وهي جملة الملفات الضرورية لإعداد البرامج وجعلها كنظام واحد قابل للتنزيل على أي جهاز حاسوب.
5. أنواع البرمجيات (Types of software) يمكن تقسيم المنتجات البرمجية من وجهة نظر السوق إلى نوعين:
* برمجيات عامة (General software): هي برمجيات مستقلة يتم إنتاجها من قبل شركة ما وتصبح عامة من حيث الاستخدام وغير محتكرة على فئة محددة من المستخدمين. قد تكون مجانية أو غير مجانية وتُباع في الأسواق لأي زبون قادر على شرائها. وتكون ملكاً للشركة التي أنتجتها.
* برمجيات خاصة (Customized products): وهي البرمجيات التي يطلبها زبون محدد أو شركة معينة ولا يمكن لغيرهم استخدامها. وتُعتبر ملكاً للشركة المستخدمة لها.
علاقة مهندس البرمجيات بالتقسيم السابق تتمثل في أنه قد يعمل على تطوير أي من هذين النوعين.
6. تعريف هندسة البرمجيات وأبعادها (Definition of Software Engineering) هندسة البرمجيات (Software Engineering): هي فرع هندسي يعني بكلي جوانب إنتاج البرمجيات ابتداءً بالمراحل الأولى المتمثلة في توصيف النظام، وصولاً إلى مرحلة صيانة هذا النظام بعد أن يوضع في العمل. تهدف إلى إنتاج برمجيات ذات جودة عالية، بتكلفة معقولة وفي فترة زمنية محددة. من هذا التعريف، يمكن فهم الأبعاد التالية:
* فرع هندسي (Engineering Branch): يعني بجعل الأشياء تعمل. المهندس يوظف كلي النظريات والمنهجيات والأدوات المتاحة لإيجاد حلول لمشكلة معينة. يحاول إيجاد حل للمشكلة حتى في غياب وجود نظرية. حلوله يجب أن تكون متلائمة مع القيود التنظيمية والمالية التي يعمل ضمنها.
* كلي جوانب إنتاج البرمجيات (All Aspects of Software Production): مهندس البرمجيات لا يعني فقط بالجانب التقني (الهندسي) لعملية إنتاج البرمجيات، بل يدرس نشاطات أخرى مثل إدارة المشاريع البرمجية وتطوير نظريات ومنهجيات جديدة.
7. علاقة هندسة البرمجيات بعلوم الحاسوب (What is the difference between software engineering and computer science?) علوم الحاسوب تهتم أساساً بدراسة واستنباط النظريات والمنهجيات التي تشكل أساس الأنظمة البرمجية والحاسوبية. بينما تهتم هندسة البرمجيات بالجوانب العملية لتطوير البرمجيات وتركز على المشاكل التي يواجهها العاملون في قطاع صناعة البرمجيات. يمكن اعتبار علوم الحاسوب بمثابة الفيزياء من مهندس الكهرباء، حيث يتوجب على مهندس البرمجيات أن يكون على دراية بجوانب علوم الحاسوب. ولكن تطبيق نظريات علوم الحاسوب ليس دائماً ممكناً في الواقع العملي، وذلك بسبب عجز نظريات علوم الحاسوب عن حل بعض المشاكل البرمجية الواقعية المعقدة، مما يضطر مهندسي البرمجيات إلى اتباع أساليب غير منتظمة في تطوير برمجياتهم.
8. علاقة هندسة البرمجيات بهندسة الأنظمة (What is the difference between software engineering and system engineering?) هندسة الأنظمة تهتم بدراسة وتطوير الأنظمة التي تحتوي على البرمجيات كجزء من مكونات هذا النظام. تركز هندسة الأنظمة على كل مكونات النظام، بما في ذلك النظام البرمجي الذي يعتبر جزءاً من هذا النظام. بينما هندسة البرمجيات تركز فقط على مكونات النظام البرمجي من عتاد حاسوبي وبرمجيات. هندسة البرمجيات تعتبر جزءاً من هندسة الأنظمة، لأنها تركز فقط على المكونات البرمجية. هندسة الأنظمة تعتبر أقدم فرعاً هندسياً من هندسة البرمجيات، حيث قام الإنسان بدراسة أنظمة معقدة قبل مائة عام، أهمها الأنظمة الصناعية مثل معامل الطائرات أو المعامل الكيميائية.
9. إجرائيات تطوير البرمجيات (Software development process / Fundamental software engineering activities) إجرائية تطوير البرمجيات هي مجموعة من النشاطات التي تعني بإنتاج المنتج البرمجي بالإضافة إلى مخرجات هذه النشاطات. أي إجرائية برمجيات تتضمن النشاطات (المراحل) التالية:
1. التوصيف البرمجي (Software specification): تحديد المتطلبات وتوصيفها وتحديد القيود المفروضة عليها. يتم في هذه المرحلة فهم وظائف النظام والقيود المفروضة عليه أثناء تشغيله أو تطويره.
2. التطوير البرمجي (Software development): يتم في هذه المرحلة تصميم وبرمجة البرمجية. بناء المنتج البرمجي المطابق للتوصيف. (الأنشطة المرتبطة هي التصميم والتنفيذ).
3. إثبات صحة البرمجية (Software validation): التأكد من أن البرمجية تنجز الوظائف التي يريدها الزبون بالشكل المطلوب. إثبات صحة البرمجية لضمان تطابقها مع متطلبات وتوقعات الزبون. (التحقق من أن النظام يفي بمواصفاته واحتياجات المستخدم).
4. تطور البرمجية (Software evolution): إجراء التعديلات على البرمجية لتنفيذ وظائف أو متطلبات جديدة أو لمواكبة تقنيات حديثة. يجب أن تنمو البرمجية وتتطور مع تغير المتطلبات. (يهتم بتعديل النظام بعد أن يكون قيد الاستخدام).
10. نموذج إجرائية البرمجيات (Software process model) نموذج إجرائية البرمجيات هو أسلوب لتنظيم نشاطات إجرائية البرمجيات وفق طريقة معينة بالإضافة إلى النشاطات المتعلقة بالأشخاص ذوي الصلة بالمشروع. هو تمثيل مجرد لعملية. يمكن النظر إلى نماذج إجرائية البرمجيات من عدة زوايا (نماذج):
* نموذج تدفق العمل (Work flow model): يوضح تسلسل نشاطات إجرائية البرمجيات مع توضيح عمليات الدخل والخرج والعلاقات بين هذه الأنشطة.
* نموذج تدفق البيانات (Data flow model): يُنظر إلى إجرائية البرمجيات على أنها مجموعة من الأنشطة كل نشاط يقوم بتحويل البيانات، أي تحويل المدخلات إلى مخرجات. البيانات تتحول من شكل لآخر بواسطة الحاسوب أو الإنسان.
* نموذج الفاعل/الدور (Role / actor model): يمثل الأدوار التي يقوم بها الأشخاص المشاركون في إجرائية البرمجيات والنشاطات الملقاة على عاتقهم.
11. نماذج إجرائية البرمجيات العامة (Generic software process models) هناك ثلاثة نماذج عامة رئيسية لوصف تنظيم عمليات البرمجيات:
* النموذج الشلالي (Waterfall model): يمثل نشاطات إجرائية البرمجيات كمراحل منفصلة متتالية (التوصيف، التصميم، البرمجة، الاختبار، التشغيل والصيانة). هو من أول النماذج التي اعتُمدت لتطوير البرمجيات عام 1970. يُعرف باسم دورة حياة البرمجية (Software Development life cycle). مخرجات كل مرحلة عن تنتج مجموعة من الوثائق التي يجب الموافقة عليها قبل الانتقال للمرحلة اللاحقة. المراحل تشمل تحليل وتعريف المتطلبات، تصميم النظام والبرمجية، التنفيذ، المكاملة واختبار النظام، التوزيع، التشغيل والصيانة. العيب الرئيسي لهذا النموذج هو صعوبة استيعاب التغيير بعد بدء العملية. يُفضل استخدامه عندما تكون المتطلبات واضحة ولا يتوقع تغييرها بشكل تدريجي خلال المراحل اللاحقة. يُستخدم أيضاً عندما يكون تطوير المنتج البرمجي جزءاً من عملية تطوير نظام كامل أو عند تطوير أنظمة الزمن الحقيقي. كما يُستخدم غالباً لمشاريع هندسة الأنظمة الكبيرة حيث يتم تطوير النظام في عدة مواقع.
   * المميزات: يتطابق مع الإجرائيات المتبعة في هندسة الأنظمة، سهولة متابعة المشروع بسبب طبيعته التسلسلية، تتمتع الأنظمة المنتجة به بتوثيق جيد.
   * العيوب: مقاومته الكبيرة للتغير، طول الفترة الزمنية الفاصلة بين تحديد المتطلبات وتسليم النظام الكامل للزبائن.
* نموذج التطوير التكراري/التطوري (Iterative/Evolutionary development model): يتناول النشاطات السابقة بشكل متكرر حيث يحتوي على عدة تكرارات. هو أحد النماذج التكرارية المستخدمة لإنتاج البرمجيات عبر مراحل متداخلة، بحيث تتداخل المواصفات مع عملية التطوير. يرتكز على بناء برنامج أولي كنواة للنظام الكلي وعرضه على الزبون ليجربه ويبدي ملاحظاته. يبدأ سلسلة من التنقيحات والتعديلات وصولاً إلى النظام الملائم لمتطلبات الزبون. يمكنه أن يتلائم بسرعة مع متطلبات وتوقعات الزبون، ويساعد الزبائن على فهم وتحديد متطلباتهم بشكل أوضح تدريجياً. نجاح النمذجة الأولية يعتمد على استقبال ردود أفعال المستخدمين وتقييمهم للنموذج الأول. هناك نوعان من التطوير التطوري: التطوير الاستكشافي (Exploratory development) الذي يهدف للعمل مع العملاء وتطوير النظام النهائي من مواصفات أولية، والنماذج الأولية المؤقتة (Throw-away prototyping) التي تهدف إلى فهم متطلبات النظام، خاصة عندما تكون غير واضحة.
   * المميزات: سرعة الحصول على نسخة أولية من النظام وتسليمها للزبون مما يساعد المستخدمين على التعبير عن متطلباتهم اللاحقة بعد استخدامها، المنتجات البرمجية المنتجة بهذا النموذج تحقق متطلبات المستخدم.
   * العيوب: صعوبة متابعة عملية تطوير المشروع، الأنظمة المنتجة بهذا النموذج تكون ضعيفة الهيكلية (البنيان) ويصعب صيانتها وفهمها ولا تتمتع بتوثيق جيد. يتطلب أن يكون مهندس البرمجيات على دراية ببعض التقنيات والأدوات التي تساعد على إنتاج النسخة الأولية بشكل سريع. ضرورة تواجد المستخدم مع فريق التطوير.
   * الاستخدام: مناسب جداً عندما تكون المتطلبات غامضة وغير معروفة، عند تطوير الأنظمة الصغيرة والمتوسطة، عند تطوير الأنظمة قصيرة الأمد التي تتطلب بناءها بشكل سريع، لتطوير أجزاء من الأنظمة كبيرة الحجم مثل واجهات الاستخدام.
* النموذج المبني على إعادة الاستخدام (Component-Based Software Engineering (CBSE)): يعتمد على وجود مكونات يمكن إعادة استخدامها. هو نموذج يُستخدم في تطوير البرمجيات بالاستفادة من المكونات الجاهزة (مثل تصاميم أكواد برمجية، أنظمة برمجية جاهزة). يعتمد على مبدأين: وجود قاعدة كبيرة من المكونات البرمجية التي يمكن إعادة استخدامها، وإمكانية دمج هذه المكونات ومكاملتها مع بعضها البعض. يعتمد على إعادة الاستخدام المنهجية حيث يتم تجميع الأنظمة من المكونات الموجودة أو أنظمة جاهزة تجارياً (COTS - Commercial-off-the-shelf). المراحل تشمل تحليل المكونات، تعديل المتطلبات، تصميم النظام مع إعادة الاستخدام، التطوير والمكاملة.
   * مراحل النموذج: تحديد المتطلبات، تحليل المكونات (تحديد المكونات الجاهزة الموجودة التي يمكن أن تحقق المتطلبات)، تعديل المتطلبات (تحليل المتطلبات على ضوء المكونات الموجودة وتعديلها لملائمة المكونات)، تصميم النظام مع إعادة الاستخدام (تصميم النظام باستخدام إطار موجود مسبقاً وتنظيمه ليتوافق مع المكونات الموجودة)، التطوير والمكاملة (تطوير أجزاء النظام التي لم يتم الحصول عليها بعد ودمج المكونات الجاهزة مع الأجزاء المطورة)، إثبات صحة النظام (التأكد من صحة النظام لضمان تطابقه مع متطلبات وتوقعات الزبون).
   * المميزات: يساعد على تسليم المنتج البرمجي بسرعة، يقوم بدور هام في تقليل التكاليف والجهد والمخاطر.
   * العيوب: قد يؤدي إلى إنتاج منتج برمجي لا يتطابق مع متطلبات المستخدم بسبب تعديل بعض المتطلبات، قد لا نمتلك زمام السيطرة على تطور النظام لأننا نستخدم مكونات أنتجتها شركة أخرى.
   * الاستخدام: من الشائع اتباع هذا النموذج في تطوير البرامج القائمة على تجميع خدمات الويب التي يؤمنها العديد من المزودين.
12. تكرار العملية (Process iteration) التغيير هو من أهم الحقائق الحتمية في الحياة، والتي يمكن أن تحصل في أي وقت. التغييرات في بيئة الشركة المحيطة، التغيير في الطاقم الإداري للشركة، التغيير في التكنولوجيا، كل ذلك يفرض تغييرات. هذه التغييرات تلقي بظلالها على نظام الشركة الحاسوبي وتستدعي ألا يتم بناء إجرائيات البرمجيات لمرة واحدة بل قد تتكرر لمرات عديدة. تكرار العمل في النظام البرمجي أمر ضروري للاستجابة للمتطلبات المتغيرة. استخدام نماذج تكرارية مناسبة مع التغييرات يضمن بناء أنظمة حاسوبية متينة البناء وسهلة الصيانة. متطلبات النظام تتطور دائماً أثناء سير المشروع. لذا فإن تكرار العملية حيث تتم إعادة صياغة المراحل السابقة دائماً ما يكون جزءاً من العملية بالنسبة للأنظمة الكبيرة. يمكن تطبيق التكرار على أي من نماذج العمليات العامة.
13. نماذج تعتمد على التكرار (Iterative process models) هناك نهجان (مرتبطان) يعتمدان على التكرار:
* التسليم التدريجي (Incremental delivery): بدلاً من تسليم النظام كعملية تسليم واحدة، يتم تقسيم التطوير والتسليم إلى زيادات مع كل زيادة توفر جزءاً من الوظيفة المطلوبة. يُعطى الأولوية لمتطلبات المستخدم ويتم تضمين المتطلبات ذات الأولوية الأعلى في الزيادات المبكرة. بمجرد بدء تطوير الزيادة، يتم تجميد المتطلبات الخاصة بها على الرغم من أن متطلبات الزيادات اللاحقة يمكن أن تستمر في التطور. هو أحد النماذج التكرارية المستخدمة في تطوير البرمجيات وتسليمها للزبون على شكل نسخ صغيرة متدرجة. هو حل توافقي يجمع بين مميزات النموذج الشلالي والتطوري ويتخلص من عيوبهما.
   * مراحل النموذج: تحديد المتطلبات بخطوط عريضة (تعريف متطلبات المستخدم وتحديد أولوياتها)، توزيع المتطلبات على أجزاء النظام (تقسيم المتطلبات وتوزيعها على أجزاء النظام وتحديد عدد وخدمات كل جزء)، تصميم بنيان النظام (تصميم البنيان العام للنظام الذي يضم كل أجزاء النظام المحددة)، تطوير جزء من النظام (توصيف متطلبات الجزء الأول، تصميم بنيانه وواجهاته وقاعدة بياناته وخوارزمياته، كتابة الكود)، إثبات صحة هذا الجزء (إجراء الفحوصات والاختبارات للتأكد من صحة وظائف الجزء)، مكاملة هذا الجزء مع الأجزاء الأخرى (دمج الجزء الأول مع الجزء التالي ضمن بنيان النظام)، إثبات صحة النظام كله (التأكد من أن النسخة الجديدة التي تحتوي على أكثر من جزء تعمل بشكل صحيح).
   * المميزات: سرعة الحصول على النسخة الأولى من النظام والاستفادة منه مبكراً، تفاعل الزبون مع النسخة الأولى يساعده على توضيح متطلباته بشكل أفضل، تخفيض نسبة خطورة فشل المشروع، الأجزاء الأكثر أهمية تخضع لاختبارات واسعة ومكاملتها ترفع من كفاءتها.
   * العيوب: صعوبة تحويل المتطلبات إلى أجزاء متساوية الحجم على أرض الواقع مما يؤدي إلى استغراق بعض الأجزاء فترة زمنية طويلة لتطويرها، تأجيل الدخول في تفاصيل كل متطلب حتى مراحل لاحقة قد يؤدي إلى بروز الحاجة لأدوات جديدة لم يتم حسابها من البداية.
* التطوير الحلزوني (Spiral development): يتم تمثيل العملية كحلزون بدلاً من تسلسل أنشطة مع تراجع. كل دورة في الحلزون تمثل مرحلة في العملية. لا توجد مراحل ثابتة مثل التوصيف أو التصميم، بل يتم اختيار الدورات في الحلزون بناءً على ما هو مطلوب. يتم تقييم المخاطر وحلها بشكل صريح خلال العملية. اقترحه Boehm عام 1988. كل دورة تقسم إلى أربعة قطاعات دائرية:
   * وضع الغاية (Determine objectives): وضع أهداف واضحة ومحددة للمرحلة، معرفة القيود المفروضة، وضع خطة إدارة تفصيلية للمرحلة. (التعاون مع العميل وأصحاب المصلحة لجمع المتطلبات، دراسة الجدوى، تقدير الميزانية والموارد والوقت).
   * تقييم وتخفيض المخاطر (Risk Analysis): تحديد المخاطر وتحليل كل خطر بشكل مفصل وتحديد الخطوات الضرورية لتخفيضه. (تحديد المخاطر المحتملة المرتبطة بالمنتج، تصميم وإعداد استراتيجية المخاطر وخطة التخفيف). هذا هو المرحلة الحاسمة التي تحتاج إلى عناية فائقة.
   * التطوير وإثبات الصحة (Development & Test): تنفيذ عمليات توصيف المتطلبات، التصميم، البرمجة، والاختبار لتطوير جزء من المنتج. اختيار نموذج التطوير المناسب (مثل النمذجة المؤقتة، التحويل الصوري، النموذج الشلالي) بناءً على نوع المخاطر المحددة. (تنفيذ المتطلبات والاستراتيجيات والخطط لتطوير المنتج واختباره).
   * التخطيط للمرحلة التالية (Plaining next phase/Evaluation): مراجعة المشروع والتخطيط للدورة التالية من الحلزون إذا كان يلزم الاستمرار. (تفاعل المنتج مع العملاء وتقييمهم له، وتقديم ملاحظاتهم لتحديد المتطلبات أو الميزات التي تحتاج للإضافة أو الإزالة في التكرار التالي).
   * المميزات: أهم ما يميز النموذج الحلزوني هو اعتماده الكبير على إدارة مخاطر المشروع وتوجيهها. (يمكن أن يقلل التكاليف والمخاطر والجهد ويسرع التسليم) (هذه المميزات مذكورة ضمن النموذج المبني على إعادة الاستخدام في المصدر، ولكنها تنطبق أيضاً على النماذج التكرارية بشكل عام).
14. البرمجة الشديدة (Extreme programming - XP) البرمجة الشديدة هي نهج لتطوير البرمجيات يعتمد على تطوير وتسليم زيادات صغيرة جداً من الوظيفة. هي نسخة معدلة من نموذج التسليم التدريجي. تعتمد على التحسين المستمر للكود، مشاركة المستخدم في فريق التطوير، والبرمجة الثنائية. ربما تكون البرمجة الشديدة هي الطريقة الأسرع في تطوير البرمجيات. تتخذ XP نهجاً "شديداً" للتطوير التكراري، حيث يمكن بناء نسخ جديدة عدة مرات في اليوم، ويتم تسليم الزيادات للعملاء كل أسبوعين، ويجب تشغيل جميع الاختبارات لكل بناء ويتم قبول البناء فقط إذا نجحت الاختبارات.
* ممارسات XP:
   * التخطيط التدريجي (Incremental planning): يتم تسجيل المتطلبات على بطاقات قصص، وتُحدد القصص المطلوب تضمينها في الإصدار بناءً على الوقت المتاح وأولويتها النسبية. يقسم المطورون هذه القصص إلى "مهام" تطوير.
   * الإصدارات الصغيرة (Small releases): يتم تطوير الحد الأدنى المفيد من الوظائف الذي يوفر قيمة تجارية أولاً. تكون إصدارات النظام متكررة وتضيف وظائف بشكل تدريجي للإصدار الأول.
   * التصميم البسيط (Simple design): يتم إجراء تصميم كافٍ لتلبية المتطلبات الحالية وليس أكثر.
   * التطوير الموجه بالاختبار (Test-first development): يتم استخدام إطار عمل اختبار وحدات مؤتمت لكتابة اختبارات لوظيفة جديدة قبل تنفيذ الوظيفة نفسها.
   * إعادة الهيكلة (Refactoring): يُتوقع من جميع المطورين إعادة هيكلة الكود بشكل مستمر بمجرد اكتشاف تحسينات ممكنة في الكود. هذا يحافظ على الكود بسيطاً وقابلاً للصيانة.
   * البرمجة الزوجية (Pair programming): يعمل المطورون في أزواج، يفحصون عمل بعضهم البعض ويقدمون الدعم لضمان العمل الجيد.
   * الملكية الجماعية (Collective ownership): تعمل أزواج المطورين على جميع أجزاء النظام، بحيث لا تتطور "جزر خبرة" ويتحمل جميع المطورين المسؤولية عن كل الكود. أي شخص يمكنه تغيير أي شيء.
   * التكامل المستمر (Continuous integration): بمجرد اكتمال العمل على مهمة ما، يتم دمجها في النظام بأكمله. بعد أي عملية دمج كهذه، يجب أن تجتاز جميع اختبارات الوحدات في النظام بنجاح.
   * وتيرة مستدامة (Sustainable pace): لا يُعتبر العمل الإضافي بكميات كبيرة مقبولاً لأن التأثير الصافي غالباً ما يقلل من جودة الكود والإنتاجية على المدى المتوسط.
   * العميل في الموقع (On-site customer): يجب أن يكون ممثل عن المستخدم النهائي للنظام (العميل) متاحاً بدوام كامل لفريق XP. في عملية البرمجة الشديدة، العميل عضو في فريق التطوير ومسؤول عن جلب متطلبات النظام للفريق لتنفيذها.
15. سكروم (Scrum) منهج سكروم هو طريقة أجايل عامة، لكن تركيزه ينصب على إدارة التطوير التكراري بدلاً من ممارسات أجايل محددة. يتكون سكروم من ثلاث مراحل:
* المرحلة الأولية (The initial phase): مرحلة تخطيط موجز يتم فيها تحديد الأهداف العامة للمشروع وتصميم بنية البرنامج.

* سلسلة دورات السرعة (Sprint cycles): حيث تطور كل دورة زيادة من النظام. هذه هي المراحل الرئيسية في سكروم.

* مرحلة إغلاق المشروع (The project closure phase): يتم فيها اختتام المشروع وإكمال الوثائق المطلوبة وتقييم الدروس المستفادة.

* دورة السرعة (The Sprint cycle): السبرنتات ثابتة المدة، تتراوح عادة بين 2-4 أسابيع. نقطة البداية للتخطيط هي قائمة مهام المنتج (product backlog)، وهي قائمة بالعمل الذي يجب إنجازه في المشروع. مرحلة الاختيار تشمل كل فريق المشروع الذين يعملون مع العميل لاختيار الميزات والوظائف التي سيتم تطويرها خلال السبرنت. بمجرد الاتفاق عليها، ينظم الفريق نفسه لتطوير البرنامج. خلال هذه المرحلة، يكون الفريق معزولاً عن العميل والمنظمة، وتُمرر جميع الاتصالات عبر ما يسمى "سكروم ماستر". دور سكروم ماستر هو حماية فريق التطوير من المشتتات الخارجية. في نهاية السبرنت، يتم مراجعة العمل المنجز وعرضه على أصحاب المصلحة. ثم تبدأ دورة السبرنت التالية.

* العمل الجماعي في سكروم (Teamwork in Scrum): سكروم ماستر هو مُيسّر يقوم بترتيب الاجتماعات اليومية، ويتتبع قائمة المهام، ويسجل القرارات، ويقيس التقدم مقارنةً بالقائمة، ويتواصل مع العملاء والإدارة خارج الفريق. يحضر الفريق بأكمله اجتماعات يومية قصيرة يتبادل فيها جميع أعضاء الفريق المعلومات، ويصفون تقدمهم منذ الاجتماع الأخير، والمشاكل التي واجهتهم، وما المخطط لليوم التالي. هذا يعني أن الجميع في الفريق يعرف ما يجري، وإذا ظهرت مشاكل، يمكنهم إعادة تخطيط العمل على المدى القصير للتعامل معها.

* فوائد سكروم (Scrum benefits): يتم تقسيم المنتج إلى مجموعة من الأجزاء القابلة للإدارة والفهم. المتطلبات غير المستقرة لا تعيق التقدم. يتمتع الفريق بأكمله برؤية كل شيء، وبالتالي يتحسن التواصل داخل الفريق. يرى العملاء تسليمات في الوقت المحدد ويكتسبون ملاحظات حول كيفية عمل المنتج. يتم بناء الثقة بين العملاء والمطورين وتُخلق ثقافة إيجابية يتوقع فيها الجميع نجاح المشروع.

16. توسيع نطاق طرق أجايل (Scaling agile methods) أثبتت طرق أجايل نجاحها للمشاريع الصغيرة والمتوسطة التي يمكن تطويرها بواسطة فريق صغير يعمل في مكان واحد. يُقال أحياناً أن نجاح هذه الطرق يأتي بسبب تحسين التواصل الذي يمكن تحقيقه عندما يعمل الجميع معاً. توسيع نطاق طرق أجايل يتضمن تغييرها للتعامل مع المشاريع الأكبر والأطول حيث توجد فرق تطوير متعددة، ربما تعمل في مواقع مختلفة.
   * تطوير الأنظمة الكبيرة (Large systems development): الأنظمة الكبيرة عادة ما تكون مجموعات من أنظمة منفصلة تتواصل مع بعضها، حيث تقوم فرق منفصلة بتطوير كل نظام. غالباً ما تعمل هذه الفرق في أماكن مختلفة، وأحياناً في مناطق زمنية مختلفة. الأنظمة الكبيرة هي "أنظمة حقول سمراء" (brownfield systems)، أي أنها تتضمن وتتفاعل مع عدد من الأنظمة الموجودة. العديد من متطلبات النظام تتعلق بهذا التفاعل وبالتالي لا تفسح المجال للمرونة والتطوير التدريجي. عندما يتم دمج عدة أنظمة لإنشاء نظام واحد، جزء كبير من التطوير يتعلق بتكوين النظام بدلاً من تطوير الكود الأصلي. غالباً ما تكون الأنظمة الكبيرة وعمليات تطويرها مقيدة بقواعد وأنظمة خارجية تحد من طريقة تطويرها. الأنظمة الكبيرة تتطلب وقتاً طويلاً للشراء والتطوير. من الصعب الحفاظ على فرق متماسكة تعرف عن النظام خلال تلك الفترة حيث ينتقل الناس حتماً إلى وظائف ومشاريع أخرى. عادةً ما تحتوي الأنظمة الكبيرة على مجموعة متنوعة من أصحاب المصلحة. من المستحيل عملياً إشراك جميع أصحاب المصلحة المختلفين هؤلاء في عملية التطوير.
   * التوسيع لأعلى والتوسع للخارج (Scaling out and scaling up):
   * التوسيع لأعلى (Scaling up): يتعلق باستخدام طرق أجايل لتطوير أنظمة برمجية كبيرة لا يمكن تطويرها بواسطة فريق صغير.
   * التوسع للخارج (Scaling out): يتعلق بكيفية إدخال طرق أجايل عبر منظمة كبيرة لديها سنوات عديدة من الخبرة في تطوير البرمجيات. عند توسيع نطاق طرق أجايل، من الضروري الحفاظ على أساسيات أجايل: التخطيط المرن، الإصدارات المتكررة للنظام، التكامل المستمر، التطوير الموجه بالاختبار، والتواصل الجيد بين الفريق.
   * التوسيع لأعلى للأنظمة الكبيرة (Scaling up to large systems): لتطوير الأنظمة الكبيرة، ليس من الممكن التركيز فقط على كود النظام. تحتاج إلى مزيد من التصميم المسبق وتوثيق النظام. يجب تصميم واستخدام آليات التواصل عبر الفرق. يجب أن يشمل ذلك مكالمات هاتفية وفيديو منتظمة بين أعضاء الفريق واجتماعات إلكترونية متكررة وقصيرة حيث تقوم الفرق بتحديث بعضها البعض حول التقدم. التكامل المستمر، حيث يتم بناء النظام بأكمله في كل مرة يقوم فيها أي مطور بإدخال تغيير، يكاد يكون مستحيلاً. ومع ذلك، من الضروري الحفاظ على بناء النظام بشكل متكرر وإصدارات منتظمة للنظام.
   * التوسع للخارج للشركات الكبيرة (Scaling out to large companies): قد يتردد مديرو المشاريع الذين ليس لديهم خبرة في طرق أجايل في قبول مخاطر نهج جديد. غالباً ما تمتلك المنظمات الكبيرة إجراءات ومعايير جودة يتوقع من جميع المشاريع اتباعها، وبسبب طبيعتها البيروقراطية، فمن المرجح أن تكون غير متوافقة مع طرق أجايل. تبدو طرق أجايل تعمل بشكل أفضل عندما يكون لدى أعضاء الفريق مستوى مهارة عالٍ نسبياً. ومع ذلك، ضمن المنظمات الكبيرة، من المرجح أن يكون هناك نطاق واسع من المهارات والقدرات. قد تكون هناك مقاومة ثقافية لطرق أجايل، خاصة في المنظمات التي لديها تاريخ طويل في استخدام عمليات هندسة النظم التقليدية.
17. التطوير القائم على التخطيط مقابل التطوير الرشيق (Plan-driven and agile development)
   * التطوير القائم على التخطيط (Plan-driven development): يعتمد على مراحل تطوير منفصلة مع تخطيط مخرجات كل مرحلة مسبقاً. ليس بالضرورة أن يكون نموذج شلالي؛ يمكن أن يكون تطوير تدريجي قائم على التخطيط أيضاً. يحدث التكرار داخل الأنشطة.
   * التطوير الرشيق (Agile development): تتداخل فيه مراحل التوصيف والتصميم والتنفيذ والاختبار. يتم تحديد مخرجات عملية التطوير من خلال عملية تفاوض أثناء عملية تطوير البرنامج.
العوامل المؤثرة في الاختيار بين النهجين (Factors influencing the choice): معظم المشاريع تشمل عناصر من العمليات القائمة على التخطيط والأجايل. يعتمد تحديد التوازن على:
   * هل من المهم وجود مواصفات وتصميم تفصيليين للغاية قبل الانتقال إلى التنفيذ؟ إذا كان الأمر كذلك، فربما تحتاج إلى استخدام نهج قائم على التخطيط.
   * هل استراتيجية التسليم التدريجي، حيث تسلم البرنامج للعملاء وتحصل على ملاحظات سريعة منهم، واقعية؟ إذا كان الأمر كذلك، فكر في استخدام طرق أجايل.
   * ما حجم النظام الذي يتم تطويره؟ تكون طرق أجايل أكثر فعالية عندما يمكن تطوير النظام بواسطة فريق صغير يعمل في نفس المكان ويمكنه التواصل بشكل غير رسمي. قد لا يكون هذا ممكناً للأنظمة الكبيرة التي تتطلب فرق تطوير أكبر، لذلك قد يكون النهج القائم على التخطيط ضرورياً.
   * ما نوع النظام الذي يتم تطويره؟ قد تكون النهج القائمة على التخطيط مطلوبة للأنظمة التي تتطلب الكثير من التحليل قبل التنفيذ (مثل أنظمة الزمن الحقيقي ذات متطلبات التوقيت المعقدة).
   * ما هو عمر النظام المتوقع؟ قد تتطلب الأنظمة ذات العمر الطويل مزيداً من وثائق التصميم لتوصيل النوايا الأصلية لمطوري النظام إلى فريق الدعم.
   * ما التقنيات المتاحة لدعم تطوير النظام؟ تعتمد طرق أجايل على أدوات جيدة لتتبع التصميم المتطور.
   * كيف يتم تنظيم فريق التطوير؟ إذا كان فريق التطوير موزعاً أو إذا تم الاستعانة بمصادر خارجية لجزء من التطوير، فقد تحتاج إلى تطوير وثائق تصميم للتواصل عبر فرق التطوير.
   * هل هناك قضايا ثقافية أو تنظيمية قد تؤثر على تطوير النظام؟ المنظمات الهندسية التقليدية لديها ثقافة التطوير القائم على التخطيط، حيث أن هذا هو المعيار في الهندسة.
   * ما مدى جودة المصممين والمبرمجين في فريق التطوير؟ يُقال أحياناً أن طرق أجايل تتطلب مستويات مهارة أعلى من النهج القائمة على التخطيط التي يترجم فيها المبرمجون ببساطة تصميماً مفصلاً إلى كود.
   * هل يخضع النظام لتنظيم خارجي؟ إذا كان يجب أن يتم اعتماد نظام من قبل جهة تنظيمية خارجية (مثل FAA التي تعتمد البرمجيات الهامة لتشغيل الطائرات)، فمن المحتمل أن يُطلب منك إنتاج وثائق مفصلة كجزء من حالة سلامة النظام.
18. هندسة البرمجيات بمساعدة الحاسوب (Computer-aided software engineering - CASE) CASE هي مجموعة كبيرة من الأنظمة البرمجية التي تساعد على إجراء النشاطات المتعلقة بتطوير البرمجيات. تترافق كل المنهجيات مع مجموعة من الأدوات المساندة مثل محررات رسم المخططات ووحدات التحليل التي تتأكد من توافق النماذج مع قواعد المنهجية. ومولدات التقارير التي تساعد على كتابة التوثيق، وبعض هذه الأدوات قادرة على توليد الشفرة المصدرية انطلاقاً من نماذج النظام. هي برمجيات لدعم عمليات تطوير وتطور البرمجيات. تساهم في أتمتة النشاطات.
   * أمثلة على الأتمتة المدعومة بـ CASE: محررات رسومية لتطوير نماذج النظام، قاموس بيانات لإدارة كيانات التصميم، باني واجهة مستخدم رسومية لإنشاء واجهات المستخدم، مصححات أخطاء لدعم إيجاد الأخطاء في البرامج، مترجمات آلية لتوليد نسخ جديدة من البرنامج. لقد أدت تقنية CASE إلى تحسينات كبيرة في عملية البرمجيات، ومع ذلك، ليست هذه التحسينات بالمقدار الذي كان متوقعاً في السابق. السبب في ذلك يعود إلى أن هندسة البرمجيات تتطلب تفكيراً إبداعياً وهذا ليس قابلاً للأتمتة بسهولة. كما أن هندسة البرمجيات هي نشاط جماعي، وفي المشاريع الكبيرة، يُقضى الكثير من الوقت في تفاعلات الفريق، وتقنية CASE لا تدعم ذلك بشكل كبير.

   * تصنيف CASE (CASE classification): يساعد التصنيف على فهم الأنواع المختلفة من أدوات CASE ودعمها لأنشطة العملية.

      * منظور وظيفي (Functional perspective): تصنف الأدوات وفقاً لوظيفتها المحددة. (أمثلة: أدوات التخطيط، التحرير، إدارة التغيير، إدارة التكوين، النمذجة الأولية، دعم المنهجيات، معالجة اللغات، تحليل البرامج، الاختبار، التنقيح، التوثيق، إعادة الهندسة).
      * منظور العملية (Process perspective): تصنف الأدوات وفقاً لأنشطة العملية التي تدعمها.
      * منظور التكامل (Integration perspective): تصنف الأدوات وفقاً لتنظيمها في وحدات متكاملة.
      * تكامل CASE (CASE integration):

         * أدوات (Tools): تدعم مهام عملية فردية مثل التحقق من اتساق التصميم، تحرير النصوص، إلخ.
         * ورش عمل (Workbenches): تدعم مرحلة عملية مثل التوصيف أو التصميم. عادةً ما تتضمن عدداً من الأدوات المتكاملة.
         * بيئات (Environments): تدعم كل أو جزء كبير من عملية برنامج بأكملها. عادةً ما تتضمن عدة ورش عمل متكاملة.
19. خصائص البرمجيات الجيدة (Essential attributes of good software) تنقسم خصائص البرمجيات الجيدة إلى:
         * الخصائص الوظيفية (Functional attributes/Functional Requirements): يقصد بها أن البرمجية تحقق الوظائف (المواصفات) المطلوبة منها على أكمل وجه. (الخدمات التي يجب أن يقدمها النظام لمستخدميه وكيفية استجابته لمدخلات معينة).
         * الخصائص غير الوظيفية (Non-Functional attributes/Non-Functional Requirements): يطلق عليها مواصفات الجودة أو الصفات الأخرى. تتعلق بسلوك البرمجية عندما وضعت قيد العمل أو بهيكلتها (تنظيم الشفرة المصدرية والوثائق الخاصة بها). لا تعني وظائف محددة يقوم بها النظام بل بخصائص النظام الكلية مثل الوثوقية، زمن الاستجابة، مساحة التخزين. قد تجعل تحقيقها أكثر حرجاً من متطلبات النظام الوظيفية، لأنه في حالة الإخفاق في تحقيقها، قد يصبح النظام بأكمله غير قابل للاستخدام.
         * أنواع المتطلبات غير الوظيفية:
         * متطلبات المنتج غير الوظيفية (Non-Functional Product Requirements): تحدد مواصفات وسلوك المنتج (النظام البرمجي) مثل متطلبات الأداء المتعلقة بسرعة تنفيذ النظام للأوامر وحجم الذاكرة التي يحتاجها. ومتطلبات الوثوقية (الاحتمال المقبول لعدم إتاحة العمل واحتمال الإخفاق بمتوسط)، ومتطلبات قابلية النقل وسهولة الاستخدام.
         * متطلبات الشركة (Company Requirements): تأتي من شركة الزبون ومن شركة بناء النظام البرمجي. مثال: معايير إجرائية التطوير المتبعة، لغة البرمجة والمنهجية المستخدمة، متطلبات التسليم التي تحدد موعد تسليم النظام البرمجي ووثائقه.
         * متطلبات خارجية (External Requirements): تشمل جميع المتطلبات التي ليس لها علاقة بالنظام نفسه أو بإجرائية بناءه. مثال: متطلبات التعامل مع الأنظمة الأخرى (التشغيل البيني)، المتطلبات التشريعية التي يجب الالتزام بها لضمان التوافق مع القوانين والتشريعات، المتطلبات الأخلاقية التي تجعل النظام مقبولاً من قبل مستخدميه.
         * أمثلة على الخصائص غير الوظيفية للبرمجية الجيدة:
         * الفاعلية (Efficiency): يجب ألا يكون النظام البرمجي مفرطاً في استهلاك الموارد الحاسوبية. تشتمل على جوانب ذات صلة بالأداء مثل زمن الاستجابة، استهلاك الذاكرة، المعالجة.
         * سهولة الصيانة (Maintainability): يجب أن تتميز البرمجية الجيدة بسهولة الصيانة، وهذا يعني أنه بإمكانها أن تتغير بسهولة لتتوافق مع المتطلبات المتغيرة.
         * الاعتمادية (Reliability): يجب ألا يسبب النظام أي ضرر كبير في حال إخفاقه.
         * سهولة الاستخدام (Ease of use): يجب أن تكون البرمجية سهلة الاستخدام من قبل زبونها. تتضمن تصميم ملائم لواجهات الاستخدام وتوثيق جيد للبرمجية.
20. متطلبات النطاق (Domain Requirements) هي المتطلبات ذات الصلة بنطاق تطبيق النظام. مثال: عندما نبني نظام معالجة طبية فإن نطاق التطبيق سيكون طبياً، وعندما نبنيه لمصرف فإن النطاق المصرفي. عادة ما تحتوي هذه المتطلبات على العديد من المصطلحات والمفاهيم ذات الصلة بالنطاق المعرفي (طب، تجارة، مكتبات، عسكري، وغيرها). قد تكون متطلبات النطاق وظيفية أو غير وظيفية (قيود مفروضة على متطلبات وظيفية أو قوانين تحدد كيفية إجراء عمليات حسابية معينة). تعتبر هذه المتطلبات بالغة الأهمية لأنها تعكس أسس نطاق التطبيق التي لا يمكن تجاهلها. إذا لم يتم تحقيق هذه المتطلبات، فقد يؤدي ذلك إلى بناء منتج برمجي لا يحقق متطلبات المستخدم بشكل صحيح.
21. تصميم وتنفيذ البرمجيات (Software design and implementation) هي عملية تحويل مواصفات النظام إلى نظام قابل للتنفيذ. تنشيطة التصميم والتنفيذ مرتبطة ارتباطاً وثيقاً وقد تكون متداخلة.
         * تصميم البرمجيات (Software design): يهدف إلى تصميم بنية برمجية تحقق المواصفات. يتم في مرحلة التصميم اتخاذ القرارات المتعلقة بكيفية بناء النظام وتشغيله وإعداد مواصفات النظام التي تحدد تماماً ما سيفعله المبرمجون في مرحلة التنفيذ. تصميم النظام يعني تحديد بنيان النظام الكامل المؤلف من مجموعة من المكونات المادية والبرمجية والأشخاص والاتصال فيما بينها بما يحقق متطلبات النظام. يتم تصميم بنيان (هيكلية) النظام البرمجي وبنية البيانات (قاعدة بيانات، مكدس، ملفات) والواجهات والخوارزميات. تشمل إجرائية التصميم تطوير عدة نماذج للنظام وفق مستويات تفصيل مختلفة. نماذج التصميم تعتبر مخرجات مرحلة التصميم وتشكل همزة وصل بين متطلبات النظام وتحقيقه. يجب أن تكون النماذج مجردة قدر الإمكان وبعيدة عن التفاصيل التي قد تشتت تركيز المطور. في نفس الوقت، يجب أن تكون مفصلة بالقدر الذي يمكن المبرمجين من تحقيق النظام.

            * أنشطة إجرائية التصميم (Design process activities):
            * تصميم البنيان (Architectural design): تحديد الأنظمة الفرعية التي تشكل النظام الكلي والعلاقات فيما بينها. (مخرجاته هي System architecture).
            * التوصيف المجرد (Abstract specification): توصيف الخدمات التي يقدمها كل نظام فرعي والقيود المفروضة عليه. هذا التوصيف مجرد جداً (عام). ينفذ هذا النشاط عند تصميم الأنظمة الحرجة فقط. (مخرجاته هي Software specification).
            * تصميم الواجهات (Interface design): تصميم الواجهات للربط بين الأنظمة الفرعية. الواجهات هي العمليات التي تهم الأنظمة الفرعية الأخرى عند التفاعل مع هذا النظام الفرعي. (مخرجاته هي Interface specification).
            * تصميم المكونات (Component design): تصميم مكونات الأنظمة الفرعية التي تحقق وظائفها (قد تكون دالة معينة، كلاس، أو مجموعة منهما) وتصميم واجهات هذه المكونات. (مخرجاته هي Component specification).
            * تصميم بنى البيانات (Data structure design): تصميم بنى المعطيات الضرورية لتخزين واسترجاع بيانات النظام (جداول قاعدة بيانات، مصفوفات، مكدسات، طوابير، وغيرها). (مخرجاته هي Data structure specification).
            * تصميم الخوارزميات (Algorithm design): تصميم الخوارزميات التي يتم بواسطتها تنفيذ العمليات (تقديم الخدمات). (مخرجاته هي Algorithm specification).
            * نماذج النظام المدعومة بالمنهجيات الهيكلية (Structured methods System Models): هي أساليب منهجية لتطوير تصميم البرمجيات. يتم عادةً توثيق التصميم على شكل مجموعة من النماذج الرسومية. قد يقتصر خرج مرحلة التصميم على أوراق التوثيق ونماذج للنظام، وقد يكون أيضاً جزءاً من شيفرة البرنامج.
            * المنهجية وظيفية التوجه (Functional-oriented methodology) تدعم النماذج التالية: النموذج الهيكلي (الصندوقي) الذي يوثق الأنظمة الفرعية وعلاقتها، نموذج تدفق البيانات الذي يوثق الوظائف وكيفية تحويل البيانات، مخطط الكينونة-العلاقة (ERD) الذي يوضح التصور الأولي لقاعدة البيانات، مخطط قاعدة البيانات (DB Schema) الذي يوثق البيانات التي تحتاجها وظائف النظام لمعالجتها.
            * المنهجية كائنية التوجه (Object-oriented methodology) تدعم 12 نموذجاً منها النماذج التالية: نموذج الأغراض (Object model) الذي يظهر الصفوف التي تكون النظام وعلاقتها، نموذج التسلسل (Sequence model) الذي يظهر كيفية تفاعل الأغراض عند تنفيذ البرنامج، نموذج الحالة (State model) الذي يمثل حالات أغراض النظام المتوقعة.
            * البرمجة والتنقيح (Implementation / Programming and debugging): هي عملية ترجمة البنية إلى برنامج قابل للتنفيذ. تبدأ مرحلة البرمجة بعد الانتهاء من التصميم. يمكن استخدام أدوات CASE لتوليد أجزاء من الشيفرة البرمجية. تعتبر البرمجة نشاطاً شخصياً. يقوم المبرمجون ببعض اختبارات البرنامج لاكتشاف الأخطاء وإزالتها في عملية التنقيح.

               * إجرائية التنقيح (The debugging process): يقوم المبرمجون بعملية اختبار للشيفرة البرمجية وتنقيحها.
               * الاختبار (Testing): عملية اختبار الشيفرة البرمجية بهدف الكشف عن وجود العيوب والأخطاء وفحص مخرجات البرمجية وسلوكها التشغيلي.
               * التنقيح (Debugging): عملية تهتم بتحديد موقع العيوب في البرنامج وإزالتها عن طريق تعديل البرنامج. ثم يتم تكرار الاختبار لضمان عمل البرنامج جيداً بعد التعديلات. عملية التنقيح تعتبر جزءاً من تطوير البرمجية وإثبات صحتها.
               * مراحل الاختبار (Testing stages/phases):
               * اختبار المكونات/الوحدات (Component/unit testing): اختبار المكونات الفردية بشكل مستقل.
               * اختبار النظام (System testing): اختبار النظام ككل. اختبار الخصائص الطارئة مهم بشكل خاص. (يتضمن تنفيذ النظام باستخدام حالات اختبار مشتقة من مواصفات البيانات الحقيقية التي سيعالجها النظام).
               * اختبار القبول (Acceptance testing): اختبار ببيانات العميل للتحقق من أن النظام يلبي احتياجاته.
22. تطور البرمجيات (Software evolution) البرمجيات مرنة بطبيعتها وقابلة للتغيير. مع تغير المتطلبات من خلال ظروف العمل المتغيرة، يجب أيضاً أن تتطور البرامج التي تدعم الأعمال. على الرغم من وجود فاصل بين التطوير والتطور (الصيانة)، إلا أن هذا الفارق يتلاشى مع تناقص عدد الأنظمة الجديدة كلياً. (يهتم بتعديل النظام بعد أن يكون قيد الاستخدام). عملية تطور النظام تشمل تقييم الأنظمة الموجودة، تحديد متطلبات النظام، اقتراح تغييرات النظام، تعديل الأنظمة.
23. إجرائية راشيونال الموحدة (The Rational Unified Process - RUP) نموذج عملية حديث مشتق من العمل على UML والعملية المرتبطة بها. هي نموذج إجرائية عام وحديث ينظم عملية تطوير البرمجيات على شكل مراحل وأنشطة وتفصل بين مفهوم المرحلة عن مفهوم النشاط. تم استقاؤها خلال صياغة لغة النمذجة الموحدة (UML) وإجرائية تطوير البرمجيات الموحدة (USDP). تجمع RUP بين أفضل الممارسات الموجودة في نماذج إجرائية البرمجيات المختلفة. توصف عادة من 3 وجهات نظر:
               * منظور ديناميكي (A dynamic perspective): يظهر المراحل عبر الزمن. المراحل هي: البداية (Inception)، الإعداد (Elaboration)، البناء (Construction)، الانتقال (Transition).
               * منظور ثابت (A static perspective): يظهر أنشطة العملية (مسارات العمل/workflows). مسارات العمل تشمل: نمذجة الأعمال (Business modelling)، المتطلبات (Requirements)، التحليل والتصميم (Analysis and design)، التنفيذ (Implementation)، الاختبار (Test)، التوزيع (Deployment)، إدارة التكوين والتغيير (Configuration and change management)، إدارة المشروع (Project management)، البيئة (Environment).
               * منظور الممارسة (A practice perspective): يقترح الممارسة الجيدة. الممارسات الجيدة لـ RUP تشمل: تطوير البرمجيات بشكل متكرر، إدارة المتطلبات، استخدام البنى القائمة على المكونات، نمذجة البرمجيات بشكل مرئي، التحقق من جودة البرمجيات، التحكم في التغييرات على البرامج.
24. إدارة المشاريع (Project Management) إدارة المشاريع البرمجية تهتم بالأنشطة المتعلقة بضمان تسليم البرمجيات في الوقت المحدد وضمن الجدول الزمني. وبما يتوافق مع متطلبات الجهات المطورة والمشترية للبرمجيات. تُعد إدارة المشاريع ضرورية لأن تطوير البرمجيات يخضع دائماً لقيود الميزانية والجدول الزمني التي تحددها الجهة المطورة.
               * معايير النجاح (Success criteria): تسليم البرنامج للعميل في الموعد المتفق عليه، الحفاظ على التكاليف الإجمالية ضمن الميزانية، تقديم برنامج يلبي توقعات العميل، الحفاظ على فريق تطوير سعيد وفعال.
               * الاختلافات في إدارة البرمجيات (Software management distinctions): المنتج غير ملموس (لا يمكن رؤيته أو لمسه)، العديد من مشاريع البرمجيات هي مشاريع "مرة واحدة" (تختلف عن المشاريع السابقة ويصعب توقع المشاكل)، عمليات البرمجيات متغيرة وتعتمد على المنظمة (لا يمكن التنبؤ بشكل موثوق بمشاكل التطوير).
               * أنشطة الإدارة (Management activities):
               * تخطيط المشاريع (Project planning): يتولى مديرو المشاريع مسؤولية التخطيط، التقدير، جدولة تطوير المشروع، وتوزيع المهام على الموظفين.
               * التقارير (Reporting): يتولى مديرو المشاريع مسؤولية تقديم تقارير عن تقدم المشروع للعملاء ومديري الشركة المطورة.
               * إدارة المخاطر (Risk management): يُقيّم مديرو المشاريع المخاطر التي قد تؤثر على المشروع، ويراقبونها، ويتخذون الإجراءات اللازمة عند ظهور أي مشاكل.
               * إدارة الأفراد (People management): يتعين على مديري المشاريع اختيار أعضاء فريقهم ووضع أساليب عمل تُسهم في أداء الفريق بفعالية.
               * كتابة المقترحات (Proposal writing): قد تتضمن المرحلة الأولى كتابة مقترح للفوز بعقد يتضمن وصفاً لأهداف المشروع وكيفية تنفيذه.
25. إدارة المخاطر (Risk management) تُعنى إدارة المخاطر بتحديد المخاطر ووضع الخطط اللازمة للحد من تأثيرها على المشروع. الخطر هو احتمال حدوث ظروف سلبية.
               * أنواع المخاطر (Risk types):

                  * مخاطر المشروع (Project risks): تؤثر على الجدول الزمني أو الموارد. (مثل ترك الموظفين ذوي الخبرة، تغيير الإدارة، عدم توفر الأجهزة، تغيير المتطلبات، تأخر المواصفات، تقدير حجم أقل من الواقع).
                  * مخاطر المنتج (Product risks): تؤثر على جودة أو أداء البرنامج قيد التطوير. (مثل عدم أداء أدوات CASE كما هو متوقع، تغيير المتطلبات، تأخر المواصفات، تقدير حجم أقل من الواقع، مكونات برمجية قابلة لإعادة الاستخدام تحتوي على عيوب).
                  * مخاطر الأعمال (Business risks): تؤثر على المؤسسة التي تُطّور أو تشتري البرنامج. (مثل تغيير التكنولوجيا الأساسية، وجود منتج منافس، مشاكل مالية للمنظمة، إعادة هيكلة المنظمة).
                  * عملية إدارة المخاطر (The risk management process):

                     * تحديد المخاطر (Risk identification): تحديد مخاطر المشروع، المنتج، والأعمال. قد يكون نشاطاً جماعياً أو يعتمد على خبرة مدير المشروع الفردية. يمكن استخدام قائمة مرجعية بالمخاطر الشائعة. أنواع المخاطر التي يمكن تحديدها: تقنية، بشرية، تنظيمية، أدوات، متطلبات، تقدير.
                     * تحليل المخاطر (Risk analysis): تقييم احتمال وخطورة كل خطر. الاحتمال قد يكون منخفضاً جداً، منخفضاً، معتدلاً، عالياً، أو عالياً جداً. عواقب المخاطر قد تكون كارثية، خطيرة، مقبولة، أو غير مهمة.
                     * التخطيط للمخاطر (Risk planning): وضع استراتيجية لإدارة كل خطر. تشمل استراتيجيات التجنب (تقليل احتمال حدوث الخطر)، استراتيجيات التخفيف (تقليل تأثير الخطر)، خطط الطوارئ (خطط للتعامل مع الخطر إذا حدث). (أمثلة على الاستراتيجيات مذكورة مثل إعداد وثيقة للمديرين التنفيذيين، تنبيه العميل، إعادة تنظيم الفريق، استبدال المكونات المعيبة، اشتقاق معلومات التتبع لتقييم تأثير تغييرات المتطلبات).
                     * مراقبة المخاطر (Risk monitoring): تقييم كل خطر محدد بانتظام لتحديد ما إذا كان احتماله يتناقص أو يتزايد. وكذلك تقييم ما إذا كانت آثار الخطر قد تغيرت. يجب مناقشة كل خطر رئيسي في اجتماعات تقدم الإدارة.
                     * مؤشرات المخاطر (Risk indicators): هي علامات محتملة على أن خطراً معيناً قد يحدث. أمثلة: تأخر تسليم الأجهزة/البرامج الداعمة ومشاكل تقنية كثيرة (خطر تقني)، انخفاض معنويات الموظفين وعلاقات سيئة بين أعضاء الفريق ودوران مرتفع للموظفين (خطر بشري)، نميمة تنظيمية وعدم اتخاذ إجراءات من الإدارة العليا (خطر تنظيمي)، تردد أعضاء الفريق في استخدام الأدوات وشكاوى حول أدوات CASE (خطر أدوات)، طلبات تغيير متطلبات كثيرة وشكاوى العملاء (خطر متطلبات)، الفشل في الوفاء بالجدول الزمني المتفق عليه والفشل في إصلاح العيوب المبلغ عنها (خطر التقدير).
26. هندسة المتطلبات (Requirements engineering) تُعتبر هندسة المتطلبات (توصيف المتطلبات) من أهم المشاكل التي تواجه بناء الأنظمة البرمجية الكبيرة والمعقدة. تهتم بفهم الوظائف والخدمات التي يجب أن يقوم بها النظام، والقيود المفروضة عليه أثناء تشغيله وإجرائية تطويره، والطرق والأساليب والأدوات المستخدمة في ذلك. لا يمكن النظر إليها كإجرائية تقنية بحتة. هي إجرائية تواصل مستمر بين مستخدمي النظام وزبائنه من جهة ومطوري النظام من جهة أخرى. تتأثر كثيراً بما يعجب المستخدم وما لا يعجبه، وبالآراء المسبقة، وبالعديد من القضايا السياسية والمهنية. هي عملية تحديد الخدمات المطلوبة والقيود المفروضة على تشغيل وتطوير النظام. (هي عملية تطوير مواصفات البرنامج).
                     * عملية هندسة المتطلبات (Requirements engineering process):

                        * دراسة الجدوى (Feasibility study): تقييم ما إذا كان النظام ممكناً من الناحية الفنية والعملية والاقتصادية. (ينتج عنها تقرير جدوى - Feasibility report).
                        * اختيار وتحليل المتطلبات (Requirements elicitation and analysis): العمل مع العملاء لتحديد المتطلبات وتصنيفها وتنظيمها وفهمها. (ينتج عنها نماذج نظام - System models).
                        * تحديد المتطلبات (Requirements specification): صياغة المتطلبات بشكل دقيق وغير غامض. (ينتج عنها متطلبات المستخدم والنظام - User and system requirements).
                        * التحقق من صحة المتطلبات (Requirements validation): التحقق من أن المتطلبات تمثل حقاً ما يريده العميل. (ينتج عنها وثيقة المتطلبات - Requirements document).
                        * متطلبات النظام (System requirements): هي جملة الخدمات (الوظائف) والعمليات التي يتوقعها المستخدم من النظام والقيود المفروضة عليها أثناء تشغيل أو بناء النظام. تعكس حاجة الزبون لهذا النظام الذي يفترض أن يحل له مشكلة معينة. يجب أخذ المتطلبات بالدرجة الأساسية من مستخدمي النظام ومن البيئة المحيطة بالنظام أيضاً.

                           * صياغة المتطلبات: يتم صياغتها باستخدام عبارات خاصة باللغة البشرية (العربية، الإنجليزية، وغيرها)، مخططات رسومية ونماذج مثل مخططات UML ومخططات DFD، رموز ومعادلات رياضية خاصة بالآلات مثل آلات الحالات المنتهية والمجموعات.
27. التحقق وإثبات صحة البرمجيات (Software validation / Verification and validation (V & V)) هي الإجرائية التي يخضع خلالها المنتج البرمجي لعملية اختبار وتدقيق دائمة لضمان تطابقه مع التوصيف وبأنه يحقق الوظائف التي يتوقعها الزبائن. يطلق عليها إجرائية V&V. تشمل العديد من عمليات الاختبار والمعاينات والمراجعات التي نقوم بها طيلة إجرائية البرمجيات. (يتضمن التحقق من أن النظام يفي بمواصفاته واحتياجات المستخدم).
                           * معاينات البرمجية (Software reviews): هي عملية تحليل وتدقيق استاتيكية لمخرجات إجرائية التطوير الملموسة التي تمثل النظام البرمجي. مثل وثيقة المتطلبات، مخططات التصميم، والشيفرة البرمجية.
                           * اختبار البرمجية (Software testing): تقنية ديناميكية تنفذ على النظام البرمجي للكشف عن الأخطاء والعيوب وتفحص مخرجات البرمجية وسلوكها التشغيلي للتأكد من أنها تقوم بوظائفها بالشكل المطلوب.
28. أخلاقيات هندسة البرمجيات والمسؤوليات (Software engineering ethics) هناك جوانب أخلاقية على مهندس البرمجيات أن يلتزم بها:
                           1. السرية (Confidentiality): احترام سرية زبائنك ورؤسائك، سواء كان هناك ميثاق أخلاقي يفرض ذلك أم لا.
                           2. الكفاءة (Competence): يجب أن تقر وتعترف بإمكانياتك ومعارفك وألا تدعي معرفة أو تعلماً لما يقع خارج مجال اختصاصك.
                           3. حقوق الملكية الفكرية (Intellectual property rights): عليك أن تكون واعياً لكل القوانين المتعلقة بحقوق الملكية الفكرية مثل براءات الاختراع وحقوق النسخ.
                           4. تجنب سوء استخدام الحواسيب (Avoiding Computer misuse): يجب ألا تستخدم ما وهبك الله من معارف وتقنيات في إساءة استخدام الحواسيب، مثل اللعب في أجهزة الغير أو نشر بعض الفيروسات الخطيرة.
آمل أن يكون هذا الشرح المفصل مفيداً لك في فهم المعلومات الواردة في المصادر.