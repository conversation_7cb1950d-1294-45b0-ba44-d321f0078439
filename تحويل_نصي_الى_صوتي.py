import pyttsx3

# فتح الملف النصي
with open("ملخص هندسة البرمجيات.txt", "r", encoding="utf-8") as file:
    text = file.read()

# تهيئة محرك الصوت
engine = pyttsx3.init()

# تعيين اللغة (قد تحتاج إلى تعيين صوت يدعم العربية)
voices = engine.getProperty('voices')
for voice in voices:
    if 'Arabic' in voice.name or 'ar' in voice.id:
        engine.setProperty('voice', voice.id)
        break

# إعداد السرعة إن أردت (اختياري)
engine.setProperty('rate', 150)

# حفظ الصوت في ملف
engine.save_to_file(text, "ملخص_هندسة_البرمجيات.mp3")
engine.runAndWait()
